# -*- coding: utf-8 -*-
import sys
import sqlite3
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QLineEdit, QPushButton, QMessageBox, QFrame,
    QStackedWidget
)
from PyQt6.QtGui import QFont, QColor, QPalette
from PyQt6.QtCore import Qt, QSize
import hashlib # لتشفير كلمات المرور
import os # للتحقق من وجود ملف قاعدة البيانات

# --- مدير قاعدة البيانات (Database Manager) ---
class DatabaseManager:
    def __init__(self, db_name="gym_management.db"):
        self.db_name = db_name
        self.conn = None
        self.cursor = None
        self.connect()
        self.create_tables()

    def connect(self):
        """يتصل بقاعدة البيانات."""
        try:
            self.conn = sqlite3.connect(self.db_name)
            self.cursor = self.conn.cursor()
        except sqlite3.Error as e:
            print(f"خطأ في الاتصال بقاعدة البيانات: {e}")
            QMessageBox.critical(None, "خطأ في قاعدة البيانات", f"تعذر الاتصال بقاعدة البيانات: {e}")
            sys.exit(1) # إنهاء التطبيق إذا لم يتمكن من الاتصال بقاعدة البيانات

    def create_tables(self):
        """ينشئ الجداول إذا لم تكن موجودة."""
        try:
            self.cursor.execute('''
                CREATE TABLE IF NOT EXISTS admin_users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT NOT NULL UNIQUE,
                    password_hash TEXT NOT NULL
                )
            ''')
            self.cursor.execute('''
                CREATE TABLE IF NOT EXISTS members (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    phone TEXT UNIQUE,
                    email TEXT UNIQUE,
                    address TEXT,
                    registration_date TEXT NOT NULL,
                    status TEXT NOT NULL DEFAULT 'نشط'
                )
            ''')
            self.cursor.execute('''
                CREATE TABLE IF NOT EXISTS subscription_plans (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL UNIQUE,
                    duration_months INTEGER NOT NULL,
                    price_lyd REAL NOT NULL,
                    discount_percentage REAL DEFAULT 0.0,
                    final_price_lyd REAL NOT NULL,
                    is_active INTEGER NOT NULL DEFAULT 1
                )
            ''')
            self.cursor.execute('''
                CREATE TABLE IF NOT EXISTS subscriptions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    member_id INTEGER NOT NULL,
                    plan_id INTEGER NOT NULL,
                    start_date TEXT NOT NULL,
                    end_date TEXT NOT NULL,
                    status TEXT NOT NULL DEFAULT 'نشط',
                    FOREIGN KEY (member_id) REFERENCES members(id),
                    FOREIGN KEY (plan_id) REFERENCES subscription_plans(id)
                )
            ''')
            self.cursor.execute('''
                CREATE TABLE IF NOT EXISTS payments (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    member_id INTEGER NOT NULL,
                    subscription_id INTEGER,
                    amount_lyd REAL NOT NULL,
                    payment_method TEXT NOT NULL,
                    payment_date TEXT NOT NULL,
                    status TEXT NOT NULL DEFAULT 'مكتملة',
                    transaction_id TEXT,
                    notes TEXT,
                    FOREIGN KEY (member_id) REFERENCES members(id),
                    FOREIGN KEY (subscription_id) REFERENCES subscriptions(id)
                )
            ''')
            self.cursor.execute('''
                CREATE TABLE IF NOT EXISTS attendance (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    member_id INTEGER NOT NULL,
                    date TEXT NOT NULL,
                    time_in TEXT NOT NULL,
                    time_out TEXT,
                    FOREIGN KEY (member_id) REFERENCES members(id)
                )
            ''')
            self.conn.commit()
            self.add_initial_admin_user() # إضافة مستخدم إداري افتراضي
        except sqlite3.Error as e:
            print(f"خطأ في إنشاء الجداول: {e}")
            QMessageBox.critical(None, "خطأ في قاعدة البيانات", f"تعذر إنشاء الجداول: {e}")
            sys.exit(1)

    def add_initial_admin_user(self):
        """يضيف مستخدم إداري افتراضي إذا لم يكن موجودًا."""
        try:
            self.cursor.execute("SELECT COUNT(*) FROM admin_users")
            if self.cursor.fetchone()[0] == 0:
                username = "admin"
                password = "password123" # كلمة مرور افتراضية
                hashed_password = hashlib.sha256(password.encode('utf-8')).hexdigest()
                self.cursor.execute("INSERT INTO admin_users (username, password_hash) VALUES (?, ?)",
                                    (username, hashed_password))
                self.conn.commit()
                print("تم إضافة مستخدم إداري افتراضي: admin/password123")
        except sqlite3.Error as e:
            print(f"خطأ في إضافة المستخدم الإداري الافتراضي: {e}")

    def authenticate_admin(self, username, password):
        """يتحقق من بيانات اعتماد المسؤول."""
        hashed_password = hashlib.sha256(password.encode('utf-8')).hexdigest()
        self.cursor.execute("SELECT * FROM admin_users WHERE username = ? AND password_hash = ?",
                            (username, hashed_password))
        return self.cursor.fetchone() is not None

    def close(self):
        """يغلق الاتصال بقاعدة البيانات."""
        if self.conn:
            self.conn.close()

# --- شاشة تسجيل الدخول (Login Window) ---
class LoginWindow(QMainWindow):
    def __init__(self, db_manager):
        super().__init__()
        self.db_manager = db_manager
        self.setWindowTitle("نظام إدارة الصالة الرياضية - تسجيل الدخول")
        self.setFixedSize(400, 300) # حجم ثابت
        self.setup_ui()
        self.center_window()

    def center_window(self):
        """توسيط النافذة على الشاشة."""
        screen_geometry = QApplication.primaryScreen().geometry()
        x = (screen_geometry.width() - self.width()) // 2
        y = (screen_geometry.height() - self.height()) // 2
        self.move(x, y)

    def setup_ui(self):
        """إعداد واجهة المستخدم لشاشة تسجيل الدخول."""
        # لوحة الألوان
        palette = self.palette()
        palette.setColor(QPalette.ColorRole.Window, QColor("#FFFFFF")) # خلفية بيضاء
        self.setPalette(palette)

        main_layout = QVBoxLayout()
        main_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # إطار لتجميع عناصر تسجيل الدخول وتطبيق التصميم
        login_frame = QFrame(self)
        login_frame.setStyleSheet("""
            QFrame {
                background-color: #FFFFFF;
                border: 1px solid #E0E0E0;
                border-radius: 15px;
                padding: 30px;
                box-shadow: 0px 4px 15px rgba(0, 0, 0, 0.1);
            }
            QLabel {
                color: #333333;
                font-size: 16px;
                margin-bottom: 5px;
            }
            QLineEdit {
                border: 1px solid #D0D0D0;
                border-radius: 8px;
                padding: 10px;
                font-size: 15px;
                background-color: #F8F8F8;
                color: #333333;
            }
            QLineEdit:focus {
                border: 2px solid #4A90E2; /* أزرق عند التركيز */
            }
            QPushButton {
                background-color: #4A90E2; /* أزرق أساسي */
                color: white;
                border-radius: 10px;
                padding: 12px 25px;
                font-size: 16px;
                font-weight: bold;
                border: none;
                transition: background-color 0.3s ease;
            }
            QPushButton:hover {
                background-color: #347DCF; /* أزرق أغمق عند التحويم */
            }
            QPushButton:pressed {
                background-color: #286BB8; /* أزرق أغمق عند الضغط */
            }
            #titleLabel {
                font-size: 24px;
                font-weight: bold;
                color: #4A90E2; /* أزرق للعناوين */
                margin-bottom: 20px;
            }
        """)
        login_layout = QVBoxLayout(login_frame)
        login_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # العنوان
        title_label = QLabel("تسجيل الدخول", objectName="titleLabel")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        login_layout.addWidget(title_label)

        # اسم المستخدم
        username_label = QLabel("اسم المستخدم:")
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("أدخل اسم المستخدم")
        self.username_input.setAlignment(Qt.AlignmentFlag.AlignRight) # محاذاة النص لليمين
        login_layout.addWidget(username_label)
        login_layout.addWidget(self.username_input)

        # كلمة المرور
        password_label = QLabel("كلمة المرور:")
        self.password_input = QLineEdit()
        self.password_input.setEchoMode(QLineEdit.EchoMode.Password)
        self.password_input.setPlaceholderText("أدخل كلمة المرور")
        self.password_input.setAlignment(Qt.AlignmentFlag.AlignRight) # محاذاة النص لليمين
        login_layout.addWidget(password_label)
        login_layout.addWidget(self.password_input)

        # زر تسجيل الدخول
        login_button = QPushButton("تسجيل الدخول")
        login_button.clicked.connect(self.attempt_login)
        login_layout.addWidget(login_button)

        main_layout.addWidget(login_frame)
        central_widget = QWidget()
        central_widget.setLayout(main_layout)
        self.setCentralWidget(central_widget)

    def attempt_login(self):
        """محاولة تسجيل الدخول."""
        username = self.username_input.text()
        password = self.password_input.text()

        if self.db_manager.authenticate_admin(username, password):
            QMessageBox.information(self, "نجاح", "تم تسجيل الدخول بنجاح!")
            self.main_window = MainWindow(self.db_manager)
            self.main_window.showMaximized() # فتح النافذة الرئيسية بحجم أقصى
            self.close() # إغلاق نافذة تسجيل الدخول
        else:
            QMessageBox.warning(self, "خطأ في تسجيل الدخول", "اسم المستخدم أو كلمة المرور غير صحيحة.")
            self.username_input.clear()
            self.password_input.clear()

# --- النافذة الرئيسية (Main Window - Dashboard Placeholder) ---
class MainWindow(QMainWindow):
    def __init__(self, db_manager):
        super().__init__()
        self.db_manager = db_manager
        self.setWindowTitle("نظام إدارة الصالة الرياضية - لوحة التحكم")
        self.setMinimumSize(1024, 768) # حجم أدنى للنافذة
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة المستخدم للنافذة الرئيسية."""
        # لوحة الألوان الأساسية
        palette = self.palette()
        palette.setColor(QPalette.ColorRole.Window, QColor("#F0F2F5")) # خلفية رمادية فاتحة
        self.setPalette(palette)

        # الخطوط
        font = QFont("Arial", 12) # يمكن تغيير الخط إلى خط عربي مناسب
        self.setFont(font)

        # الهيكل الرئيسي
        main_widget = QWidget()
        main_layout = QHBoxLayout(main_widget)
        self.setCentralWidget(main_widget)

        # الشريط الجانبي (Sidebar)
        sidebar = QFrame(self)
        sidebar.setFixedWidth(250)
        sidebar.setStyleSheet("""
            QFrame {
                background-color: #4A90E2; /* أزرق داكن للشريط الجانبي */
                border-radius: 15px;
                padding: 15px;
                color: white;
            }
            QPushButton {
                background-color: #347DCF; /* أزرق أغمق للأزرار */
                color: white;
                border-radius: 8px;
                padding: 12px;
                font-size: 16px;
                font-weight: bold;
                border: none;
                text-align: right; /* محاذاة النص لليمين */
                padding-right: 20px; /* مسافة داخلية لليمين */
                margin-bottom: 10px;
            }
            QPushButton:hover {
                background-color: #286BB8; /* أزرق أغمق عند التحويم */
            }
            QPushButton:checked {
                background-color: #2ECC71; /* أخضر عند الاختيار */
                border: 2px solid #FFFFFF; /* حدود بيضاء */
            }
            QLabel#sidebarTitle {
                font-size: 22px;
                font-weight: bold;
                color: white;
                margin-bottom: 30px;
                text-align: center;
            }
        """)
        sidebar_layout = QVBoxLayout(sidebar)
        sidebar_layout.setAlignment(Qt.AlignmentFlag.AlignTop | Qt.AlignmentFlag.AlignRight) # محاذاة العناصر للأعلى واليمين

        sidebar_title = QLabel("نظام إدارة الصالة", objectName="sidebarTitle")
        sidebar_title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        sidebar_layout.addWidget(sidebar_title)

        # زر لوحة التحكم
        btn_dashboard = QPushButton("لوحة التحكم")
        btn_dashboard.setCheckable(True)
        btn_dashboard.setChecked(True) # تحديد لوحة التحكم كالمحدد افتراضيا
        sidebar_layout.addWidget(btn_dashboard)

        # زر إدارة الأعضاء
        btn_members = QPushButton("إدارة الأعضاء")
        btn_members.setCheckable(True)
        sidebar_layout.addWidget(btn_members)

        # زر إدارة الاشتراكات
        btn_subscriptions = QPushButton("إدارة الاشتراكات")
        btn_subscriptions.setCheckable(True)
        sidebar_layout.addWidget(btn_subscriptions)

        # زر إدارة المدفوعات
        btn_payments = QPushButton("إدارة المدفوعات")
        btn_payments.setCheckable(True)
        sidebar_layout.addWidget(btn_payments)

        # زر التقارير والإحصائيات
        btn_reports = QPushButton("التقارير والإحصائيات")
        btn_reports.setCheckable(True)
        sidebar_layout.addWidget(btn_reports)

        # مسافة مرنة لدفع الأزرار للأعلى
        sidebar_layout.addStretch()

        # زر تسجيل الخروج
        btn_logout = QPushButton("تسجيل الخروج")
        btn_logout.setStyleSheet("""
            QPushButton {
                background-color: #E74C3C; /* أحمر للخروج */
                color: white;
                border-radius: 8px;
                padding: 10px;
                font-size: 14px;
                font-weight: bold;
                border: none;
                margin-top: 20px;
            }
            QPushButton:hover {
                background-color: #C0392B;
            }
        """)
        btn_logout.clicked.connect(self.logout)
        sidebar_layout.addWidget(btn_logout)

        main_layout.addWidget(sidebar)

        # منطقة المحتوى الرئيسية (Stacked Widget)
        self.content_area = QStackedWidget(self)
        self.content_area.setStyleSheet("""
            QStackedWidget {
                background-color: #F0F2F5; /* خلفية المحتوى */
                border-radius: 15px;
                padding: 20px;
            }
            QLabel {
                color: #333333;
            }
        """)
        main_layout.addWidget(self.content_area)

        # صفحات المحتوى (Placeholders)
        self.dashboard_page = self.create_dashboard_page()
        self.members_page = self.create_placeholder_page("إدارة الأعضاء قريباً...")
        self.subscriptions_page = self.create_placeholder_page("إدارة الاشتراكات قريباً...")
        self.payments_page = self.create_placeholder_page("إدارة المدفوعات قريباً...")
        self.reports_page = self.create_placeholder_page("التقارير والإحصائيات قريباً...")

        self.content_area.addWidget(self.dashboard_page)
        self.content_area.addWidget(self.members_page)
        self.content_area.addWidget(self.subscriptions_page)
        self.content_area.addWidget(self.payments_page)
        self.content_area.addWidget(self.reports_page)

        # ربط الأزرار بالصفحات
        btn_dashboard.clicked.connect(lambda: self.content_area.setCurrentWidget(self.dashboard_page))
        btn_members.clicked.connect(lambda: self.content_area.setCurrentWidget(self.members_page))
        btn_subscriptions.clicked.connect(lambda: self.content_area.setCurrentWidget(self.subscriptions_page))
        btn_payments.clicked.connect(lambda: self.content_area.setCurrentWidget(self.payments_page))
        btn_reports.clicked.connect(lambda: self.content_area.setCurrentWidget(self.reports_page))

        # مجموعة أزرار الشريط الجانبي لضمان اختيار واحد فقط
        self.sidebar_buttons = [btn_dashboard, btn_members, btn_subscriptions, btn_payments, btn_reports]
        for button in self.sidebar_buttons:
            button.clicked.connect(self.handle_sidebar_button_click)

    def handle_sidebar_button_click(self):
        """يضمن أن زرًا واحدًا فقط في الشريط الجانبي محدد."""
        sender_button = self.sender()
        for button in self.sidebar_buttons:
            if button is not sender_button:
                button.setChecked(False)
            else:
                button.setChecked(True)

    def create_placeholder_page(self, text):
        """ينشئ صفحة عنصر نائب بسيطة."""
        page = QWidget()
        layout = QVBoxLayout(page)
        label = QLabel(text)
        label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        label.setFont(QFont("Arial", 24, QFont.Weight.Bold))
        label.setStyleSheet("color: #666666;")
        layout.addWidget(label)
        return page

    def create_dashboard_page(self):
        """ينشئ صفحة لوحة التحكم."""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setAlignment(Qt.AlignmentFlag.AlignTop | Qt.AlignmentFlag.AlignRight)

        title_label = QLabel("لوحة التحكم الرئيسية")
        title_label.setFont(QFont("Arial", 28, QFont.Weight.Bold))
        title_label.setStyleSheet("color: #333333; margin-bottom: 20px;")
        layout.addWidget(title_label)

        # بطاقات الإحصائيات
        stats_layout = QHBoxLayout()
        stats_layout.setAlignment(Qt.AlignmentFlag.AlignRight) # محاذاة البطاقات لليمين

        # بطاقة الأعضاء النشطين
        active_members_card = self.create_stat_card("الأعضاء النشطون", "0", "#4A90E2")
        stats_layout.addWidget(active_members_card)

        # بطاقة الإيرادات الشهرية
        monthly_revenue_card = self.create_stat_card("الإيرادات الشهرية", "0.00 د.ل", "#2ECC71")
        stats_layout.addWidget(monthly_revenue_card)

        # بطاقة الاشتراكات المنتهية قريباً
        expiring_subscriptions_card = self.create_stat_card("اشتراكات قيد الانتهاء", "0", "#F39C12")
        stats_layout.addWidget(expiring_subscriptions_card)

        layout.addLayout(stats_layout)
        layout.addStretch() # لدفع المحتوى للأعلى

        return page

    def create_stat_card(self, title, value, color):
        """ينشئ بطاقة إحصائية."""
        card_frame = QFrame()
        card_frame.setFixedSize(250, 150)
        card_frame.setStyleSheet(f"""
            QFrame {{
                background-color: #FFFFFF;
                border-radius: 15px;
                padding: 20px;
                box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.08);
                border-left: 8px solid {color}; /* شريط لوني جانبي */
            }}
            QLabel {{
                color: #333333;
            }}
            QLabel#cardTitle {{
                font-size: 18px;
                font-weight: bold;
                margin-bottom: 10px;
            }}
            QLabel#cardValue {{
                font-size: 36px;
                font-weight: bold;
                color: {color};
            }}
        """)
        card_layout = QVBoxLayout(card_frame)
        card_layout.setAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignTop)

        title_label = QLabel(title, objectName="cardTitle")
        title_label.setAlignment(Qt.AlignmentFlag.AlignRight)
        card_layout.addWidget(title_label)

        value_label = QLabel(value, objectName="cardValue")
        value_label.setAlignment(Qt.AlignmentFlag.AlignRight)
        card_layout.addWidget(value_label)

        card_layout.addStretch()
        return card_frame

    def logout(self):
        """تسجيل الخروج من التطبيق."""
        reply = QMessageBox.question(self, "تسجيل الخروج", "هل أنت متأكد أنك تريد تسجيل الخروج؟",
                                     QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                                     QMessageBox.StandardButton.No)
        if reply == QMessageBox.StandardButton.Yes:
            self.close()
            self.login_window = LoginWindow(self.db_manager)
            self.login_window.show()

# --- نقطة دخول التطبيق (Application Entry Point) ---
if __name__ == "__main__":
    app = QApplication(sys.argv)

    # ضبط الخط الافتراضي للتطبيق لدعم اللغة العربية بشكل أفضل (اختياري)
    # قد تحتاج إلى تثبيت خطوط عربية على النظام لكي تعمل بشكل صحيح
    font = QFont("Arial") # يمكن تغيير هذا إلى خط عربي مثل "Cairo" أو "Almarai"
    font.setPointSize(12)
    app.setFont(font)

    # التأكد من أن الاتجاه من اليمين لليسار (RTL)
    QApplication.setLayoutDirection(Qt.LayoutDirection.RightToLeft)

    db_manager = DatabaseManager()
    login_window = LoginWindow(db_manager)
    login_window.show()

    sys.exit(app.exec())
